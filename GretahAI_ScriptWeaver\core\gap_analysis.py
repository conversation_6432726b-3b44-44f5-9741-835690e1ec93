"""
Gap Analysis Module for GretahAI ScriptWeaver Stage 10.

This module provides AI-powered gap analysis functionality for template-based script generation.
It analyzes template scripts and target test cases to identify missing information needed
for successful script generation, then provides dynamic forms for users to fill in the gaps.

Functions:
    analyze_template_gaps: Main function for AI-powered gap analysis
    display_gap_filling_form: Dynamic form generation for gap filling
    _build_gap_analysis_prompt: Internal function for prompt generation
    _parse_gap_analysis_response: Internal function for response parsing
    _validate_gap_analysis_result: Internal function for result validation
"""

import json
import logging
import time
import streamlit as st
from typing import Dict, Any, List, Optional
from datetime import datetime

# Configure logging
logger = logging.getLogger("ScriptWeaver.core.gap_analysis")

# Import debug utilities
from debug_utils import debug

# Import enhanced Stage 10 logging
from core.stage10_logger import (
    get_stage10_logger, log_gap_analysis, log_interactive_selector,
    log_session_state, log_ui_interaction, log_process, log_ai_request,
    log_operation
)


def analyze_template_gaps(
    template_script: Dict[str, Any],
    target_test_case: Dict[str, Any],
    api_key: str = None,
    model_name: str = "gemini-2.0-flash"
) -> Optional[Dict[str, Any]]:
    """
    Analyze template script and target test case to identify gaps and missing information.

    This function uses AI to compare a template script with a target test case and identify
    specific gaps such as missing test data, undefined locators, missing configuration
    parameters, and incompatible step sequences.

    Args:
        template_script: The optimized script to use as template
        target_test_case: The target test case for generation
        api_key: Google AI API key
        model_name: AI model to use for analysis

    Returns:
        Dict containing gap analysis results with keys:
        - gaps: List of identified gaps with type, description, and suggested values
        - analysis_summary: Summary of the analysis
        - confidence_level: AI confidence in the analysis
        - recommendations: General recommendations for script generation
    """
    with log_operation("analyze_template_gaps", "gap_analysis"):
        try:
            # Log gap analysis start with detailed context
            template_id = template_script.get('id', 'unknown')
            target_id = target_test_case.get('Test Case ID', 'unknown')

            log_gap_analysis("analysis_start",
                           template_id=template_id,
                           target_id=target_id,
                           model=model_name,
                           has_api_key=bool(api_key))

            debug("Starting template gap analysis")

            # Import here to avoid circular imports
            from .ai import generate_llm_response

            # Build the gap analysis prompt
            with log_operation("build_gap_analysis_prompt", "gap_analysis.prompt"):
                analysis_prompt = _build_gap_analysis_prompt(template_script, target_test_case)

                if not analysis_prompt:
                    log_gap_analysis("prompt_build_failed")
                    debug("Failed to build gap analysis prompt")
                    return None

                log_gap_analysis("prompt_built", prompt_length=len(analysis_prompt))

            # Call the AI through centralized function
            debug(f"Sending gap analysis request to {model_name}")

            context = {
                'template_script_id': template_id,
                'target_test_case_id': target_id,
                'analysis_type': 'template_gap_analysis'
            }

            # Log AI request details
            start_time = time.time()
            log_ai_request("gap_analysis", model_name, len(analysis_prompt))

            response_text = generate_llm_response(
                prompt=analysis_prompt,
                model_name=model_name,
                api_key=api_key,
                context=context,
                category="gap_analysis",
                function_name="analyze_template_gaps"
            )

            # Log AI response details
            duration_ms = (time.time() - start_time) * 1000
            if response_text:
                log_ai_request("gap_analysis", model_name, len(analysis_prompt),
                             response_length=len(response_text), duration_ms=duration_ms)
                log_gap_analysis("ai_response_received",
                               response_length=len(response_text),
                               duration_ms=duration_ms)
            else:
                log_ai_request("gap_analysis", model_name, len(analysis_prompt),
                             duration_ms=duration_ms, error="No response received")
                log_gap_analysis("ai_response_failed", duration_ms=duration_ms)
                debug("No response received from gap analysis AI call")
                return None

            # Parse the AI response
            with log_operation("parse_gap_analysis_response", "gap_analysis.parsing"):
                gap_analysis_result = _parse_gap_analysis_response(response_text)

                if gap_analysis_result:
                    log_gap_analysis("response_parsed_successfully",
                                   raw_gaps_count=len(gap_analysis_result.get('gaps', [])))

                    # Validate the result structure
                    validated_result = _validate_gap_analysis_result(gap_analysis_result)
                    gaps_count = len(validated_result.get('gaps', []))

                    log_gap_analysis("analysis_completed",
                                   gaps_found=gaps_count,
                                   confidence=validated_result.get('confidence_level', 'unknown'))

                    debug(f"Gap analysis completed successfully, found {gaps_count} gaps")
                    return validated_result
                else:
                    log_gap_analysis("response_parsing_failed")
                    debug("Failed to parse gap analysis response")
                    return None

        except Exception as e:
            log_gap_analysis("analysis_error", error=str(e))
            logger.error(f"Gap analysis failed: {e}")
            debug(f"Gap analysis error: {e}")
            return None


def display_gap_filling_form(gaps: List[Dict[str, Any]], form_key: str, website_url: str = None) -> Optional[Dict[str, Any]]:
    """
    Display a dynamic form for users to fill in identified gaps.

    This function creates a Streamlit form with appropriate input widgets based on
    the gap types identified by the AI analysis. It handles different input types
    such as text, numbers, selections, and boolean values. For locator gaps, it
    provides both manual text input and interactive element selection options.

    Args:
        gaps: List of gap dictionaries from gap analysis
        form_key: Unique key for the form to maintain state
        website_url: Website URL for interactive element selection (optional)

    Returns:
        Dict of gap_id -> user_provided_value if form is submitted and valid,
        None if form is not yet submitted or incomplete
    """
    with log_operation("display_gap_filling_form", "gap_analysis.form"):
        try:
            # Log form display start with context
            log_gap_analysis("form_display_start",
                           gaps_count=len(gaps),
                           form_key=form_key,
                           website_url=website_url,
                           has_website_url=bool(website_url))

            debug(f"Displaying gap filling form with {len(gaps)} gaps")

            if not gaps:
                log_gap_analysis("form_no_gaps")
                return {}

            # Check for interactive selector results in session state
            with log_operation("handle_interactive_selector_results", "gap_analysis.form.selector"):
                _handle_interactive_selector_results()

            # Check for pending interactive selector requests and launch them
            with log_operation("check_and_launch_pending_selectors", "gap_analysis.form.selector"):
                _check_and_launch_pending_selectors(gaps, website_url)

            # Poll for interactive selector results
            with log_operation("poll_interactive_selector_results", "gap_analysis.form.selector"):
                _poll_interactive_selector_results(gaps)

            # First, render interactive selector buttons outside the form for locator gaps
            locator_gaps = [gap for gap in gaps if gap.get('type') == 'locator']
            debug(f"Gap analysis: Found {len(locator_gaps)} locator gaps, website_url: {website_url}")

            log_gap_analysis("form_locator_gaps_analysis",
                           total_gaps=len(gaps),
                           locator_gaps=len(locator_gaps),
                           website_url=website_url)

            # Allow interactive selector even with example.com for testing purposes
            if locator_gaps and website_url and website_url.startswith(('http://', 'https://')):
                log_gap_analysis("form_interactive_selector_enabled",
                               locator_gaps_count=len(locator_gaps))

                st.markdown("**Interactive Element Selection:**")
                st.info("Click the buttons below to visually select elements for locator gaps, then fill in the form.")

                # Create columns for interactive selector buttons
                cols = st.columns(min(len(locator_gaps), 3))  # Max 3 columns

                for i, gap in enumerate(locator_gaps):
                    gap_id = gap.get('id', f'gap_{i}')
                    description = gap.get('description', f'Gap {i+1}')

                    with cols[i % len(cols)]:
                        # Prevent rapid successive clicks on interactive selector buttons
                        selector_in_progress_key = f"interactive_selector_in_progress_{gap_id}"
                        selector_pending_key = f"interactive_selector_pending_{gap_id}"

                        if st.session_state.get(selector_in_progress_key, False):
                            st.info("🔄 Selector in progress...")
                        elif st.session_state.get(selector_pending_key, False):
                            st.info("🚀 Launching browser...")
                        else:
                            if st.button(
                                f"👆 Select: {description[:20]}{'...' if len(description) > 20 else ''}",
                                key=f"interactive_select_{gap_id}",
                                help=f"Open browser to select element for: {description}",
                                use_container_width=True
                            ):
                                # Mark selector as pending to trigger browser launch on next rerun
                                st.session_state[selector_pending_key] = True
                                debug(f"Interactive selector button clicked for gap {gap_id}, triggering rerun")
                                st.rerun()
                                return None  # Exit early to trigger rerun

                st.markdown("---")
            else:
                # Provide feedback when interactive selector is not available
                log_gap_analysis("form_interactive_selector_disabled",
                               reason="condition_failed",
                               has_locator_gaps=bool(locator_gaps),
                               has_website_url=bool(website_url),
                               website_url_valid=website_url.startswith(('http://', 'https://')) if website_url else False)

                if locator_gaps:
                    if not website_url:
                        debug("Interactive selector not available: No website URL configured")
                        st.info("💡 Configure a website URL in the Configuration section above to enable interactive element selection for locator gaps.")
                    elif not website_url.startswith(('http://', 'https://')):
                        debug(f"Interactive selector not available: Invalid URL format: {website_url}")
                        st.warning("⚠️ Website URL must start with http:// or https:// to enable interactive element selection.")
                    else:
                        debug(f"Interactive selector not available: Unknown reason. URL: {website_url}, Locator gaps: {len(locator_gaps)}")
                else:
                    debug("No locator gaps found - interactive selector not needed")

            # Prevent duplicate form submissions
            form_submission_key = f"gap_form_submitted_{form_key}"
            if st.session_state.get(form_submission_key, False):
                log_gap_analysis("form_duplicate_submission_prevented", form_key=form_key)
                debug("Gap filling form already submitted, preventing duplicate submission")
                st.info("🔄 Processing your gap analysis submission...")
                return None

            # Create form for gap filling
            with st.form(key=f"gap_filling_form_{form_key}"):
                st.markdown("**Please provide the missing information:**")

                user_inputs = {}

                for i, gap in enumerate(gaps):
                    gap_id = gap.get('id', f'gap_{i}')
                    gap_type = gap.get('type', 'text')
                    description = gap.get('description', f'Gap {i+1}')
                    suggested_values = gap.get('suggested_values', [])
                    required = gap.get('required', True)

                    # Create appropriate input widget based on gap type
                    if gap_type == 'test_data':
                        if suggested_values:
                            # Use selectbox for predefined options
                            value = st.selectbox(
                                f"🔧 {description}",
                                options=[''] + suggested_values,
                                key=f"gap_{gap_id}",
                                help=f"Select or provide: {description}"
                            )
                        else:
                            # Use text input for custom values
                            value = st.text_input(
                                f"🔧 {description}",
                                key=f"gap_{gap_id}",
                                help=f"Enter value for: {description}",
                                placeholder="Enter test data value..."
                            )

                    elif gap_type == 'locator':
                        # Simplified locator input (interactive selector handled outside form)
                        value = _render_locator_gap_input_form_safe(gap_id, description, suggested_values, website_url)

                    elif gap_type == 'url':
                        value = st.text_input(
                            f"🌐 {description}",
                            key=f"gap_{gap_id}",
                            help=f"Enter URL: {description}",
                            placeholder="https://example.com/page"
                        )

                    elif gap_type == 'configuration':
                        if suggested_values:
                            value = st.selectbox(
                                f"⚙️ {description}",
                                options=[''] + suggested_values,
                                key=f"gap_{gap_id}",
                                help=f"Select configuration: {description}"
                            )
                        else:
                            value = st.text_input(
                                f"⚙️ {description}",
                                key=f"gap_{gap_id}",
                                help=f"Enter configuration value: {description}",
                                placeholder="Enter configuration value..."
                            )

                    elif gap_type == 'timeout':
                        value = st.number_input(
                            f"⏱️ {description}",
                            min_value=1,
                            max_value=60,
                            value=10,
                            key=f"gap_{gap_id}",
                            help=f"Enter timeout in seconds: {description}"
                        )

                    elif gap_type == 'boolean':
                        value = st.checkbox(
                            f"☑️ {description}",
                            key=f"gap_{gap_id}",
                            help=f"Enable/disable: {description}"
                        )

                    else:  # Default to text input
                        value = st.text_input(
                            f"📝 {description}",
                            key=f"gap_{gap_id}",
                            help=f"Enter value: {description}",
                            placeholder="Enter value..."
                        )

                    # Store the value if provided
                    if gap_type == 'boolean' or (value and str(value).strip()):
                        user_inputs[gap_id] = value
                    elif required:
                        # Mark as incomplete if required field is empty
                        user_inputs[gap_id] = None

                # Form submission
                col1, col2 = st.columns(2)

                with col1:
                    submitted = st.form_submit_button(
                        "✅ Fill Gaps & Generate Script",
                        use_container_width=True,
                        type="primary"
                    )

                with col2:
                    skip_gaps = st.form_submit_button(
                        "⏭️ Skip Gap Analysis",
                        use_container_width=True,
                        help="Generate script without filling gaps (may result in lower quality)"
                    )

                # Handle form submission with proper state management
                if submitted:
                    # Mark form as submitted to prevent duplicates
                    st.session_state[form_submission_key] = True

                    # Check if all required fields are filled
                    missing_required = [gap['description'] for gap in gaps
                                     if gap.get('required', True) and
                                     user_inputs.get(gap.get('id', f'gap_{gaps.index(gap)}')) is None]

                    if missing_required:
                        st.error(f"❌ Please fill in required fields: {', '.join(missing_required)}")
                        # Clear submission flag to allow retry
                        st.session_state[form_submission_key] = False
                        return None

                    # Filter out None values and return valid inputs
                    valid_inputs = {k: v for k, v in user_inputs.items() if v is not None}
                    debug(f"Gap filling form submitted with {len(valid_inputs)} valid inputs")

                    # Clear submission flag after successful processing
                    st.session_state[form_submission_key] = False
                    return valid_inputs

                elif skip_gaps:
                    # Mark form as submitted to prevent duplicates
                    st.session_state[form_submission_key] = True
                    debug("User chose to skip gap analysis")

                    # Clear submission flag after processing
                    st.session_state[form_submission_key] = False
                    return {}  # Return empty dict to proceed without gap filling

                return None  # Form not submitted yet

        except Exception as e:
            log_gap_analysis("form_error", error=str(e))
            logger.error(f"Error displaying gap filling form: {e}")
            debug(f"Gap filling form error: {e}")
            st.error(f"❌ Error displaying form: {e}")
            return None


def _build_gap_analysis_prompt(template_script: Dict[str, Any], target_test_case: Dict[str, Any]) -> str:
    """
    Build a comprehensive prompt for AI-powered gap analysis.

    Args:
        template_script: The template script to analyze
        target_test_case: The target test case to compare against

    Returns:
        str: The gap analysis prompt
    """
    try:
        # Extract key information from template and target
        template_content = template_script.get('content', '')
        template_metadata = template_script.get('metadata', {})
        template_test_case_id = template_metadata.get('test_case_id', 'Unknown')
        
        target_test_case_id = target_test_case.get('Test Case ID', 'Unknown')
        target_objective = target_test_case.get('Test Case Objective', 'No objective specified')
        target_steps = target_test_case.get('Steps', [])

        # Build the gap analysis prompt
        prompt = f"""## Role: You are a senior QA automation engineer specializing in test script analysis.

## Task: Gap Analysis for Template-Based Script Generation

Analyze the provided template script and target test case to identify gaps and missing information that could prevent successful script generation.

### Template Script Information
- **Original Test Case ID**: {template_test_case_id}
- **Template Type**: Optimized automation script
- **Template Content**:
```python
{template_content[:2000]}{'...' if len(template_content) > 2000 else ''}
```

### Target Test Case Information
- **Test Case ID**: {target_test_case_id}
- **Objective**: {target_objective}
- **Number of Steps**: {len(target_steps)}
- **Steps Summary**:
{chr(10).join([f"  {i+1}. {step.get('action', 'N/A')} - {step.get('expected_result', 'N/A')}" for i, step in enumerate(target_steps[:10])])}
{'  ...' if len(target_steps) > 10 else ''}

## Analysis Instructions

Identify specific gaps in the following categories:

1. **Test Data Gaps**: Missing usernames, passwords, input values, or test data parameters
2. **Locator Gaps**: Undefined or incompatible element selectors/locators
3. **Configuration Gaps**: Missing URLs, timeouts, or environment-specific settings
4. **Step Sequence Gaps**: Incompatible or missing step sequences between template and target
5. **Assertion Gaps**: Missing or incompatible verification/assertion requirements

## Response Format

Provide your analysis as JSON:

```json
{{
  "gaps": [
    {{
      "id": "gap_1",
      "type": "test_data",
      "description": "Username for login step",
      "required": true,
      "suggested_values": ["testuser", "admin", "<EMAIL>"],
      "impact": "high",
      "category": "authentication"
    }},
    {{
      "id": "gap_2", 
      "type": "locator",
      "description": "CSS selector for submit button",
      "required": true,
      "suggested_values": ["#submit-btn", ".submit-button", "button[type='submit']"],
      "impact": "medium",
      "category": "ui_interaction"
    }}
  ],
  "analysis_summary": "Found 2 critical gaps that need to be addressed for successful script generation",
  "confidence_level": "high",
  "recommendations": [
    "Provide specific test data values for authentication",
    "Verify element selectors match target application"
  ]
}}
```

**Gap Types**: test_data, locator, url, configuration, timeout, boolean
**Impact Levels**: high, medium, low
**Required**: true/false indicating if gap must be filled

Return only the JSON response."""

        debug("Built gap analysis prompt successfully")
        return prompt

    except Exception as e:
        logger.error(f"Error building gap analysis prompt: {e}")
        debug(f"Gap analysis prompt error: {e}")
        return ""


def _parse_gap_analysis_response(response_text: str) -> Optional[Dict[str, Any]]:
    """
    Parse the AI response for gap analysis.

    Args:
        response_text: Raw AI response text

    Returns:
        Dict containing parsed gap analysis results, or None if parsing fails
    """
    try:
        debug("Parsing gap analysis response")
        
        # Try to extract JSON from the response
        response_text = response_text.strip()
        
        # Look for JSON block markers
        if '```json' in response_text:
            start = response_text.find('```json') + 7
            end = response_text.find('```', start)
            if end != -1:
                json_text = response_text[start:end].strip()
            else:
                json_text = response_text[start:].strip()
        elif response_text.startswith('{') and response_text.endswith('}'):
            json_text = response_text
        else:
            # Try to find JSON-like content
            start = response_text.find('{')
            end = response_text.rfind('}') + 1
            if start != -1 and end > start:
                json_text = response_text[start:end]
            else:
                debug("No JSON content found in response")
                return None

        # Parse the JSON
        gap_analysis_result = json.loads(json_text)
        debug("Successfully parsed gap analysis JSON response")
        return gap_analysis_result

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse gap analysis JSON: {e}")
        debug(f"JSON parse error: {e}")
        debug(f"Response text: {response_text[:500]}...")
        return None
    except Exception as e:
        logger.error(f"Error parsing gap analysis response: {e}")
        debug(f"Gap analysis parsing error: {e}")
        return None


def _validate_gap_analysis_result(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize gap analysis result structure.

    Args:
        result: Raw gap analysis result from AI

    Returns:
        Dict with validated and normalized structure
    """
    try:
        debug("Validating gap analysis result structure")
        
        # Ensure required keys exist
        validated_result = {
            'gaps': result.get('gaps', []),
            'analysis_summary': result.get('analysis_summary', 'Gap analysis completed'),
            'confidence_level': result.get('confidence_level', 'medium'),
            'recommendations': result.get('recommendations', [])
        }

        # Validate and normalize gaps
        validated_gaps = []
        for i, gap in enumerate(validated_result['gaps']):
            if isinstance(gap, dict):
                validated_gap = {
                    'id': gap.get('id', f'gap_{i}'),
                    'type': gap.get('type', 'text'),
                    'description': gap.get('description', f'Gap {i+1}'),
                    'required': gap.get('required', True),
                    'suggested_values': gap.get('suggested_values', []),
                    'impact': gap.get('impact', 'medium'),
                    'category': gap.get('category', 'general')
                }
                validated_gaps.append(validated_gap)

        validated_result['gaps'] = validated_gaps
        
        debug(f"Validated gap analysis result with {len(validated_gaps)} gaps")
        return validated_result

    except Exception as e:
        logger.error(f"Error validating gap analysis result: {e}")
        debug(f"Gap analysis validation error: {e}")
        # Return minimal valid structure
        return {
            'gaps': [],
            'analysis_summary': 'Validation failed',
            'confidence_level': 'low',
            'recommendations': []
        }


def _render_locator_gap_input_form_safe(gap_id: str, description: str, suggested_values: List[str], website_url: str = None) -> str:
    """
    Render form-safe locator input without interactive selector buttons (for use inside st.form).

    Args:
        gap_id: Unique identifier for the gap
        description: Description of the locator gap
        suggested_values: List of suggested locator values
        website_url: Website URL for interactive element selection (not used in form-safe version)

    Returns:
        str: The selected or entered locator value
    """
    try:
        # Check if we have a previously selected locator from interactive selector
        interactive_key = f"interactive_locator_{gap_id}"
        selected_locator = st.session_state.get(interactive_key, "")

        # Show info about interactive selection if available
        if selected_locator:
            st.info(f"🎯 Interactive selection: `{selected_locator}` (click 'Select' button above to change)")

        if suggested_values:
            # Use selectbox for predefined options with custom option
            options = [''] + suggested_values + ['Custom...']

            # If we have an interactive selection, try to match it or set to Custom
            default_index = 0
            if selected_locator:
                if selected_locator in suggested_values:
                    default_index = suggested_values.index(selected_locator) + 1
                else:
                    default_index = len(options) - 1  # Custom...

            selected_option = st.selectbox(
                f"🎯 {description}",
                options=options,
                index=default_index,
                key=f"gap_{gap_id}_select",
                help=f"Select a suggested locator or choose 'Custom...' to enter your own"
            )

            # If custom is selected, show text input
            if selected_option == 'Custom...':
                value = st.text_input(
                    "Custom Locator",
                    value=selected_locator,
                    key=f"gap_{gap_id}_custom",
                    help=f"Enter CSS selector or XPath: {description}",
                    placeholder="e.g., #username, .submit-btn, //button[@type='submit']"
                )
            else:
                value = selected_option or selected_locator
        else:
            # Use text input for custom values
            value = st.text_input(
                f"🎯 {description}",
                value=selected_locator,
                key=f"gap_{gap_id}",
                help=f"Enter CSS selector or XPath: {description}",
                placeholder="e.g., #username, .submit-btn, //button[@type='submit']"
            )

        return value

    except Exception as e:
        logger.error(f"Error rendering form-safe locator gap input: {e}")
        debug(f"Form-safe locator gap input error: {e}")
        # Fallback to simple text input
        return st.text_input(
            f"🎯 {description}",
            key=f"gap_{gap_id}_fallback",
            help=f"Enter CSS selector or XPath: {description}",
            placeholder="e.g., #username, .submit-btn, //button[@type='submit']"
        )


def _render_locator_gap_input(gap_id: str, description: str, suggested_values: List[str], website_url: str = None) -> str:
    """
    Render enhanced locator input with both manual text input and interactive selector options.

    NOTE: This function is deprecated in favor of the new form-safe approach.
    Interactive selector buttons are now rendered outside the form.

    Args:
        gap_id: Unique identifier for the gap
        description: Description of the locator gap
        suggested_values: List of suggested locator values
        website_url: Website URL for interactive element selection

    Returns:
        str: The selected or entered locator value
    """
    # Delegate to form-safe version
    return _render_locator_gap_input_form_safe(gap_id, description, suggested_values, website_url)


def _launch_interactive_selector_for_gap(gap_id: str, description: str, website_url: str):
    """
    Launch the interactive element selector for a specific gap using a non-blocking approach.

    This function launches a browser in a separate process and sets up polling
    to check for results, making it compatible with Streamlit's execution model.

    Args:
        gap_id: Unique identifier for the gap
        description: Description of the locator gap
        website_url: Website URL for interactive element selection
    """
    with log_operation(f"launch_interactive_selector_{gap_id}", "interactive_selector"):
        try:
            log_interactive_selector("launch_start", gap_id,
                                   description=description,
                                   website_url=website_url)

            debug(f"Launching interactive selector for gap {gap_id}: {description}")

            # Validate inputs - allow example.com for testing but warn user
            if not website_url:
                log_interactive_selector("launch_failed", gap_id,
                                        reason="no_website_url",
                                        website_url=website_url)
                st.error("❌ Please configure a website URL before using interactive element selection")
                return

            if website_url == "https://example.com":
                st.warning("⚠️ Using example.com URL - this may not work for actual element selection. Please configure a real website URL for best results.")
                debug(f"Launching interactive selector with example.com URL for gap {gap_id}")

            if not website_url.startswith(('http://', 'https://')):
                log_interactive_selector("launch_failed", gap_id,
                                        reason="invalid_url_format",
                                        website_url=website_url)
                st.error("❌ Website URL must start with http:// or https://")
                return

            # Import required modules
            try:
                import subprocess
                import json
                import tempfile
                import os
                import sys
                import time
            except ImportError as import_error:
                debug(f"Failed to import required modules: {import_error}")
                st.error("❌ Required modules not available for interactive selector")
                return

            # Create a temporary file to store the result
            temp_dir = tempfile.gettempdir()
            result_file = os.path.join(temp_dir, f"gretah_selector_result_{gap_id}.json")

            # Store the result file path in session state for polling
            st.session_state[f"interactive_selector_result_file_{gap_id}"] = result_file
            st.session_state[f"interactive_selector_start_time_{gap_id}"] = time.time()

            # Create a Python script to run the interactive selector
            selector_script = f'''
import sys
import json
import time
import traceback
from pathlib import Path

# Add the GretahAI_ScriptWeaver directory to Python path
# Since this script runs from temp directory, find the project root dynamically
import os

def find_gretah_scriptweaver_root():
    # Try multiple strategies to find the GretahAI_ScriptWeaver directory

    # Strategy 1: Check if we're in a known project structure
    current_drive = os.path.splitdrive(os.getcwd())[0]
    common_paths = [
        os.path.join(current_drive, "GenAIJira", "JiraOllamaPython", "GRETAH-CaseForge", "GretahAI_ScriptWeaver"),
        os.path.join(current_drive, "GRETAH-CaseForge", "GretahAI_ScriptWeaver"),
        os.path.join(current_drive, "GretahAI_ScriptWeaver")
    ]

    for path in common_paths:
        if os.path.exists(path) and os.path.exists(os.path.join(path, "core", "interactive_selector.py")):
            return path

    # Strategy 2: Search from current working directory upwards
    cwd = os.getcwd()
    search_paths = [
        cwd,
        os.path.dirname(cwd),
        os.path.dirname(os.path.dirname(cwd)),
        os.path.dirname(os.path.dirname(os.path.dirname(cwd)))
    ]

    for base_path in search_paths:
        candidate = os.path.join(base_path, "GretahAI_ScriptWeaver")
        if os.path.exists(candidate) and os.path.exists(os.path.join(candidate, "core", "interactive_selector.py")):
            return candidate

    return None

script_weaver_root = find_gretah_scriptweaver_root()
if script_weaver_root:
    sys.path.insert(0, script_weaver_root)
    print(f"Found GretahAI_ScriptWeaver at: {{script_weaver_root}}")
else:
    print("Warning: Could not locate GretahAI_ScriptWeaver directory")

try:
    from core.interactive_selector import launch_interactive_selector

    print("Starting interactive element selector...")
    result = launch_interactive_selector("{website_url}")

    # Save result to file
    result_file_path = r"{result_file}"
    with open(result_file_path, "w") as f:
        json.dump({{
            "success": True,
            "result": result,
            "gap_id": "{gap_id}",
            "description": "{description}",
            "timestamp": time.time()
        }}, f)

    print("Interactive selector completed successfully")

except Exception as e:
    print(f"Error in interactive selector: {{e}}")
    traceback.print_exc()

    # Save error to file
    result_file_path = r"{result_file}"
    with open(result_file_path, "w") as f:
        json.dump({{
            "success": False,
            "error": str(e),
            "gap_id": "{gap_id}",
            "description": "{description}",
            "timestamp": time.time()
            }}, f)
'''

            # Write the script to a temporary file
            script_file = os.path.join(temp_dir, f"gretah_selector_script_{gap_id}.py")
            with open(script_file, "w") as f:
                f.write(selector_script)

                # Launch the script in a separate process
                try:
                    # Create log files for debugging
                    log_dir = temp_dir
                    stdout_log = os.path.join(log_dir, f"gretah_selector_stdout_{gap_id}.log")
                    stderr_log = os.path.join(log_dir, f"gretah_selector_stderr_{gap_id}.log")

                    with open(stdout_log, "w") as stdout_file, open(stderr_log, "w") as stderr_file:
                        process = subprocess.Popen([
                            sys.executable, script_file
                        ],
                        stdout=stdout_file,
                        stderr=stderr_file,
                        # Don't create new console in Streamlit environment - it can cause issues
                        creationflags=0 if sys.platform == "win32" else 0
                        )

                    # Store process info for debugging
                    process_info = {
                        "pid": process.pid,
                        "stdout_log": stdout_log,
                        "stderr_log": stderr_log,
                        "script_file": script_file
                    }
                    st.session_state[f"interactive_selector_process_{gap_id}"] = process_info

                    log_interactive_selector("process_launched", gap_id,
                                            pid=process.pid,
                                            stdout_log=stdout_log,
                                            stderr_log=stderr_log)

                    st.success(f"🚀 Browser launched for {description}")
                    st.info("📋 **Instructions:**\n"
                           "1. A browser window should open shortly\n"
                           "2. Navigate to the target page if needed\n"
                           "3. Hover over elements to highlight them\n"
                           "4. Click on the element you want to select\n"
                           "5. Close the browser when done\n"
                           "6. Return to this page to see the result")

                    # Set up polling for results
                    st.session_state[f"interactive_selector_polling_{gap_id}"] = True
                    log_interactive_selector("polling_enabled", gap_id)
                    debug(f"Interactive selector launched for gap {gap_id}, polling enabled")

                except Exception as launch_error:
                    log_interactive_selector("launch_process_failed", gap_id,
                                            error=str(launch_error))
                    debug(f"Failed to launch interactive selector process: {launch_error}")
                    st.error(f"❌ Failed to launch browser: {launch_error}")
                    return

        except Exception as e:
            log_interactive_selector("launch_error", gap_id, error=str(e))
            logger.error(f"Error launching interactive selector for gap {gap_id}: {e}")
            debug(f"Interactive selector error for gap {gap_id}: {e}")
            st.error(f"❌ Error launching interactive selector: {e}")
        finally:
            # Clear the in-progress flag
            selector_in_progress_key = f"interactive_selector_in_progress_{gap_id}"
            if selector_in_progress_key in st.session_state:
                st.session_state[selector_in_progress_key] = False


def _extract_best_locator(selected_element: Dict[str, Any]) -> str:
    """
    Extract the best locator from the selected element information.

    Args:
        selected_element: Element information from interactive selector

    Returns:
        str: The best locator for the element, or empty string if none found
    """
    try:
        # Priority order for locator selection
        locator_priority = [
            'id',           # Highest priority - most reliable
            'name',         # Second priority - usually reliable
            'css_selector', # Third priority - flexible
            'xpath',        # Fourth priority - most specific but fragile
            'class_name',   # Lower priority - may not be unique
            'tag_name'      # Lowest priority - least specific
        ]

        for locator_type in locator_priority:
            if locator_type in selected_element and selected_element[locator_type]:
                locator_value = selected_element[locator_type]

                # Format the locator appropriately
                if locator_type == 'id':
                    return f"#{locator_value}"
                elif locator_type == 'name':
                    return f"[name='{locator_value}']"
                elif locator_type == 'css_selector':
                    return locator_value
                elif locator_type == 'xpath':
                    return locator_value
                elif locator_type == 'class_name':
                    # Handle multiple classes
                    if ' ' in locator_value:
                        # Use the first class for simplicity
                        first_class = locator_value.split()[0]
                        return f".{first_class}"
                    else:
                        return f".{locator_value}"
                elif locator_type == 'tag_name':
                    return locator_value

        # If no standard locators found, try to construct a CSS selector
        if 'selector' in selected_element:
            return selected_element['selector']

        debug("No suitable locator found in selected element")
        return ""

    except Exception as e:
        logger.error(f"Error extracting locator from selected element: {e}")
        debug(f"Locator extraction error: {e}")
        return ""


def _handle_interactive_selector_results():
    """
    Handle any pending interactive selector results and update the form accordingly.

    This function checks for completed interactive selections and updates the
    corresponding gap inputs with the selected locators.
    """
    try:
        # Check for any pending interactive gap selections
        pending_keys = [key for key in st.session_state.keys() if key.startswith("pending_interactive_gap_")]

        for pending_key in pending_keys:
            gap_info = st.session_state[pending_key]
            gap_id = gap_info['gap_id']

            # Check if we have a result for this gap
            result_key = f"interactive_locator_{gap_id}"
            if result_key in st.session_state:
                # We have a result, clean up the pending state
                del st.session_state[pending_key]
                debug(f"Cleaned up pending interactive gap: {gap_id}")

    except Exception as e:
        logger.error(f"Error handling interactive selector results: {e}")
        debug(f"Interactive selector results handling error: {e}")


def _check_and_launch_pending_selectors(gaps: List[Dict[str, Any]], website_url: str = None):
    """
    Check for pending interactive selector requests and launch them.

    This function is called after a button click rerun to actually launch
    the interactive browser selector for element selection.

    Args:
        gaps: List of gap dictionaries from gap analysis
        website_url: Website URL for interactive element selection
    """
    try:
        debug("Checking for pending interactive selector requests")

        # Find any pending selector requests
        pending_keys = [key for key in st.session_state.keys() if key.startswith("interactive_selector_pending_")]

        if not pending_keys:
            debug("No pending interactive selector requests found")
            return

        # Process the first pending request
        for pending_key in pending_keys:
            gap_id = pending_key.replace("interactive_selector_pending_", "")
            debug(f"Found pending interactive selector request for gap {gap_id}")

            # Find the gap description
            gap_description = "Element locator"
            for gap in gaps:
                if gap.get('id') == gap_id:
                    gap_description = gap.get('description', 'Element locator')
                    break

            # Clear the pending flag and set in-progress flag
            del st.session_state[pending_key]
            in_progress_key = f"interactive_selector_in_progress_{gap_id}"
            st.session_state[in_progress_key] = True

            # Launch the interactive selector
            try:
                debug(f"Launching interactive selector for gap {gap_id}: {gap_description}")
                _launch_interactive_selector_for_gap(gap_id, gap_description, website_url)
            except Exception as e:
                debug(f"Interactive selector error for gap {gap_id}: {e}")
                st.error(f"❌ Error launching selector: {e}")
            finally:
                # Clear the in-progress flag
                if in_progress_key in st.session_state:
                    st.session_state[in_progress_key] = False

            # Only process one request at a time
            break

    except Exception as e:
        debug(f"Error checking pending interactive selectors: {e}")
        st.error(f"❌ Error processing interactive selector request: {e}")


def _poll_interactive_selector_results(gaps: List[Dict[str, Any]]):
    """
    Poll for interactive selector results from separate processes.

    This function checks for result files created by the interactive selector
    processes and processes any completed selections.

    Args:
        gaps: List of gap dictionaries from gap analysis
    """
    try:
        import json
        import os
        import time

        debug("Polling for interactive selector results")

        # Find any active polling sessions
        polling_keys = [key for key in st.session_state.keys() if key.startswith("interactive_selector_polling_")]

        if not polling_keys:
            debug("No active interactive selector polling sessions")
            return

        for polling_key in polling_keys:
            gap_id = polling_key.replace("interactive_selector_polling_", "")
            debug(f"Checking results for gap {gap_id}")

            # Get the result file path
            result_file_key = f"interactive_selector_result_file_{gap_id}"
            result_file = st.session_state.get(result_file_key)

            if not result_file or not os.path.exists(result_file):
                # Check for timeout
                start_time_key = f"interactive_selector_start_time_{gap_id}"
                start_time = st.session_state.get(start_time_key, time.time())

                if time.time() - start_time > 300:  # 5 minute timeout
                    debug(f"Interactive selector timeout for gap {gap_id}")
                    st.warning(f"⏰ Interactive selector timed out for gap {gap_id}")
                    _cleanup_interactive_selector_session(gap_id)
                else:
                    # Show polling status with debug info
                    elapsed = int(time.time() - start_time)
                    st.info(f"🔄 Waiting for element selection (gap {gap_id}) - {elapsed}s elapsed")

                    # Show debug info if process info is available
                    process_info_key = f"interactive_selector_process_{gap_id}"
                    if process_info_key in st.session_state:
                        process_info = st.session_state[process_info_key]

                        # Check log files for debugging
                        stdout_log = process_info.get("stdout_log")
                        stderr_log = process_info.get("stderr_log")

                        if stdout_log and os.path.exists(stdout_log):
                            try:
                                with open(stdout_log, 'r') as f:
                                    stdout_content = f.read().strip()
                                if stdout_content:
                                    with st.expander(f"📋 Process Output (gap {gap_id})"):
                                        st.code(stdout_content)
                            except:
                                pass

                        if stderr_log and os.path.exists(stderr_log):
                            try:
                                with open(stderr_log, 'r') as f:
                                    stderr_content = f.read().strip()
                                if stderr_content:
                                    with st.expander(f"⚠️ Process Errors (gap {gap_id})"):
                                        st.code(stderr_content)
                            except:
                                pass

                continue

            # Read the result file
            try:
                with open(result_file, 'r') as f:
                    result_data = json.load(f)

                debug(f"Found result for gap {gap_id}: {result_data}")

                if result_data.get('success'):
                    # Process successful result
                    selected_element = result_data.get('result')
                    if selected_element:
                        locator = _extract_best_locator(selected_element)
                        if locator:
                            # Store the selected locator
                            st.session_state[f"interactive_locator_{gap_id}"] = locator
                            st.success(f"✅ Element selected for gap {gap_id}: `{locator}`")
                            debug(f"Interactive selector successful for gap {gap_id}: {locator}")
                        else:
                            st.warning(f"⚠️ Could not determine a suitable locator for gap {gap_id}")
                    else:
                        st.warning(f"⚠️ No element was selected for gap {gap_id}")
                else:
                    # Process error result
                    error_msg = result_data.get('error', 'Unknown error')
                    st.error(f"❌ Interactive selector failed for gap {gap_id}: {error_msg}")
                    debug(f"Interactive selector error for gap {gap_id}: {error_msg}")

                # Clean up the session
                _cleanup_interactive_selector_session(gap_id)

                # Trigger a rerun to update the UI
                st.rerun()
                return

            except Exception as file_error:
                debug(f"Error reading result file for gap {gap_id}: {file_error}")
                st.error(f"❌ Error reading selector result for gap {gap_id}: {file_error}")
                _cleanup_interactive_selector_session(gap_id)

    except Exception as e:
        debug(f"Error polling interactive selector results: {e}")
        st.error(f"❌ Error checking selector results: {e}")


def _cleanup_interactive_selector_session(gap_id: str):
    """
    Clean up session state and temporary files for an interactive selector session.

    Args:
        gap_id: Unique identifier for the gap
    """
    try:
        import os

        debug(f"Cleaning up interactive selector session for gap {gap_id}")

        # Clean up session state keys
        cleanup_keys = [
            f"interactive_selector_polling_{gap_id}",
            f"interactive_selector_result_file_{gap_id}",
            f"interactive_selector_start_time_{gap_id}",
            f"interactive_selector_in_progress_{gap_id}",
            f"interactive_selector_pending_{gap_id}",
            f"interactive_selector_process_{gap_id}"
        ]

        for key in cleanup_keys:
            if key in st.session_state:
                del st.session_state[key]
                debug(f"Cleaned up session state key: {key}")

        # Clean up temporary files
        try:
            import tempfile
            temp_dir = tempfile.gettempdir()

            # Remove result file
            result_file = os.path.join(temp_dir, f"gretah_selector_result_{gap_id}.json")
            if os.path.exists(result_file):
                os.remove(result_file)
                debug(f"Removed result file: {result_file}")

            # Remove script file
            script_file = os.path.join(temp_dir, f"gretah_selector_script_{gap_id}.py")
            if os.path.exists(script_file):
                os.remove(script_file)
                debug(f"Removed script file: {script_file}")

            # Remove log files
            stdout_log = os.path.join(temp_dir, f"gretah_selector_stdout_{gap_id}.log")
            stderr_log = os.path.join(temp_dir, f"gretah_selector_stderr_{gap_id}.log")

            if os.path.exists(stdout_log):
                os.remove(stdout_log)
                debug(f"Removed stdout log: {stdout_log}")

            if os.path.exists(stderr_log):
                os.remove(stderr_log)
                debug(f"Removed stderr log: {stderr_log}")

        except Exception as cleanup_error:
            debug(f"Error cleaning up temporary files for gap {gap_id}: {cleanup_error}")

    except Exception as e:
        debug(f"Error cleaning up interactive selector session for gap {gap_id}: {e}")
